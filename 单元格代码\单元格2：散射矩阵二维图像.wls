(*单元格2：散射矩阵二维图像*)
(*绘制散射矩阵的透射和双侧反射二维图像*)
(*横轴：激励频率的实部，纵轴：激励声速的虚部（参数a）*)

(*透射图像*)
transmissionPlot = DensityPlot[
  Log10[bigT1tsm[f, myL, a]], {f, 1, 4200}, {a, 0, 1}, 
  PlotPoints -> 50, PlotRange -> {-5, 0}, PlotTheme -> "Scientific", 
  ColorFunction -> "SunsetColors", Frame -> False, Axes -> True, 
  PlotLabel -> 
   Style["散射矩阵透射率/dB", FontFamily -> "Source Han Serif CN"], 
  AxesLabel -> {Style["频率/Hz", FontFamily -> "Source Han Serif CN"], 
    Style["声速虚部因子a", FontFamily -> "Source Han Serif CN"]}, 
  ClippingStyle -> Automatic, PlotLegends -> Automatic, ImageSize -> 400]

(*左侧反射图像*)
leftReflectionPlot = DensityPlot[
  Log10[bigT1leftR[f, myL, a]], {f, 1, 4200}, {a, 0, 1}, 
  PlotPoints -> 50, PlotRange -> {-5, 0}, PlotTheme -> "Scientific", 
  ColorFunction -> "SunsetColors", Frame -> False, Axes -> True, 
  PlotLabel -> 
   Style["散射矩阵左侧反射率/dB", FontFamily -> "Source Han Serif CN"], 
  AxesLabel -> {Style["频率/Hz", FontFamily -> "Source Han Serif CN"], 
    Style["声速虚部因子a", FontFamily -> "Source Han Serif CN"]}, 
  ClippingStyle -> Automatic, PlotLegends -> Automatic, ImageSize -> 400]

(*右侧反射图像*)
rightReflectionPlot = DensityPlot[
  Log10[bigT1rightR[f, myL, a]], {f, 1, 4200}, {a, 0, 1}, 
  PlotPoints -> 50, PlotRange -> {-5, 0}, PlotTheme -> "Scientific", 
  ColorFunction -> "SunsetColors", Frame -> False, Axes -> True, 
  PlotLabel -> 
   Style["散射矩阵右侧反射率/dB", FontFamily -> "Source Han Serif CN"], 
  AxesLabel -> {Style["频率/Hz", FontFamily -> "Source Han Serif CN"], 
    Style["声速虚部因子a", FontFamily -> "Source Han Serif CN"]}, 
  ClippingStyle -> Automatic, PlotLegends -> Automatic, ImageSize -> 400]

(*组合显示三幅图像*)
GraphicsGrid[{{transmissionPlot}, {leftReflectionPlot}, {rightReflectionPlot}}, 
 Spacings -> {0, 20}, ImageSize -> 500]
