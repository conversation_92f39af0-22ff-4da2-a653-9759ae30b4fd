(* 双端口声学波导系统散射矩阵绘图 - 最终版 *)
(* 优化中文显示和扩展参数范围 *)

(* 编码设置 *)
$CharacterEncoding = "UTF-8";

Print["开始计算散射矩阵..."];

(* 基本参数定义 *)
c0 = 343; (* 参考声速 m/s *)

(* 复声速定义 *)
c1 = Function[a, 343*(1 + a*I)]; (* 损耗段：正虚部 *)
c2 = Function[a, 343*(1 - a*I)]; (* 增益段：负虚部 *)

(* 波数函数 *)
k = Function[{f, c}, 2*Pi*f/c];

(* 传输矩阵定义 *)
lossT = Function[{f, L, a}, 
  {{Cos[k[f, c1[a]]*L], I*Sin[k[f, c1[a]]*L]*c1[a]},
   {I*Sin[k[f, c1[a]]*L]/c1[a], Cos[k[f, c1[a]]*L]}}];

gainT = Function[{f, L, a}, 
  {{Cos[k[f, c2[a]]*L], I*Sin[k[f, c2[a]]*L]*c2[a]},
   {I*Sin[k[f, c2[a]]*L]/c2[a], Cos[k[f, c2[a]]*L]}}];

(* 总传输矩阵 *)
bigT1 = Function[{f, L, Lmid, a}, lossT[f, L, a].gainT[f, L, a]];

(* 散射矩阵元素 *)
transmission = Function[{f, L, Lmid, a}, 
  Power[2/Abs[bigT1[f, L, Lmid, a][[1, 1]] + bigT1[f, L, Lmid, a][[1, 2]]/c0 + 
    bigT1[f, L, Lmid, a][[2, 1]]*c0 + bigT1[f, L, Lmid, a][[2, 2]]], 2]];

leftReflection = Function[{f, L, Lmid, a}, 
  Power[Abs[(bigT1[f, L, Lmid, a][[1, 1]] + bigT1[f, L, Lmid, a][[1, 2]]/c0 - 
    bigT1[f, L, Lmid, a][[2, 1]]*c0 - bigT1[f, L, Lmid, a][[2, 2]])/
    (bigT1[f, L, Lmid, a][[1, 1]] + bigT1[f, L, Lmid, a][[1, 2]]/c0 + 
    bigT1[f, L, Lmid, a][[2, 1]]*c0 + bigT1[f, L, Lmid, a][[2, 2]])], 2]];

rightReflection = Function[{f, L, Lmid, a}, 
  Power[Abs[(-bigT1[f, L, Lmid, a][[1, 1]] + bigT1[f, L, Lmid, a][[1, 2]]/c0 - 
    bigT1[f, L, Lmid, a][[2, 1]]*c0 + bigT1[f, L, Lmid, a][[2, 2]])/
    (bigT1[f, L, Lmid, a][[1, 1]] + bigT1[f, L, Lmid, a][[1, 2]]/c0 + 
    bigT1[f, L, Lmid, a][[2, 1]]*c0 + bigT1[f, L, Lmid, a][[2, 2]])], 2]];

(* 参数设置 *)
L = 0.1;        (* 损耗段长度 m *)
Lmid = 0.1;     (* 增益段长度 m *)
fRange = {1, 2000};      (* 频率范围 Hz *)
aRange = {-0.2, 0.2};    (* 虚部系数范围，关于0对称 *)

Print["参数设置完成"];
Print["频率范围: ", fRange[[1]], " - ", fRange[[2]], " Hz"];
Print["虚部系数范围: ", aRange[[1]], " - ", aRange[[2]]];

(* 创建图像 - 使用中文标题但英文文件名 *)
Print["正在生成透射系数图像..."];
transmissionPlot = DensityPlot[
  transmission[f, L, Lmid, a],
  {f, fRange[[1]], fRange[[2]]},
  {a, aRange[[1]], aRange[[2]]},
  PlotLabel -> Style["透射系数 |T|²", FontSize -> 16, FontFamily -> "SimSun"],
  FrameLabel -> {
    Style["频率 (Hz)", FontSize -> 14, FontFamily -> "SimSun"], 
    Style["声速虚部系数", FontSize -> 14, FontFamily -> "SimSun"]
  },
  ColorFunction -> "SunsetColors",
  PlotLegends -> Automatic,
  ImageSize -> 600,
  PlotPoints -> 60
];

Print["正在生成左侧反射系数图像..."];
leftReflectionPlot = DensityPlot[
  leftReflection[f, L, Lmid, a],
  {f, fRange[[1]], fRange[[2]]},
  {a, aRange[[1]], aRange[[2]]},
  PlotLabel -> Style["左侧反射系数 |R_L|²", FontSize -> 16, FontFamily -> "SimSun"],
  FrameLabel -> {
    Style["频率 (Hz)", FontSize -> 14, FontFamily -> "SimSun"], 
    Style["声速虚部系数", FontSize -> 14, FontFamily -> "SimSun"]
  },
  ColorFunction -> "SunsetColors",
  PlotLegends -> Automatic,
  ImageSize -> 600,
  PlotPoints -> 60
];

Print["正在生成右侧反射系数图像..."];
rightReflectionPlot = DensityPlot[
  rightReflection[f, L, Lmid, a],
  {f, fRange[[1]], fRange[[2]]},
  {a, aRange[[1]], aRange[[2]]},
  PlotLabel -> Style["右侧反射系数 |R_R|²", FontSize -> 16, FontFamily -> "SimSun"],
  FrameLabel -> {
    Style["频率 (Hz)", FontSize -> 14, FontFamily -> "SimSun"], 
    Style["声速虚部系数", FontSize -> 14, FontFamily -> "SimSun"]
  },
  ColorFunction -> "SunsetColors",
  PlotLegends -> Automatic,
  ImageSize -> 600,
  PlotPoints -> 60
];

(* 导出图像 *)
Print["导出透射系数图像..."];
Export["transmission_coefficient_final.png", transmissionPlot, "PNG"];

Print["导出左侧反射系数图像..."];
Export["left_reflection_coefficient_final.png", leftReflectionPlot, "PNG"];

Print["导出右侧反射系数图像..."];
Export["right_reflection_coefficient_final.png", rightReflectionPlot, "PNG"];

(* 组合图像 *)
combinedPlot = GraphicsGrid[
  {{transmissionPlot}, {leftReflectionPlot}, {rightReflectionPlot}}, 
  ImageSize -> 1000, 
  Spacings -> {20, 20},
  Frame -> All,
  FrameStyle -> Directive[Gray, Thickness[1]]
];

Print["导出组合图像..."];
Export["scattering_matrix_final.png", combinedPlot, "PNG"];

Print["所有图像导出完成！"];
Print["生成的文件："];
Print["- transmission_coefficient_final.png (透射系数)"];
Print["- left_reflection_coefficient_final.png (左侧反射系数)"];
Print["- right_reflection_coefficient_final.png (右侧反射系数)"];
Print["- scattering_matrix_final.png (组合图像)"];

Print[""];
Print["计算完成！参数总结："];
Print["损耗段长度: ", L, " m"];
Print["增益段长度: ", Lmid, " m"];
Print["频率范围: ", fRange[[1]], " - ", fRange[[2]], " Hz"];
Print["虚部系数范围: ", aRange[[1]], " - ", aRange[[2]], " (关于0对称)"];
Print["颜色方案: SunsetColors"];
Print["分辨率: 60x60 点"];
