(*单元格1：基本定义*)

(* ========== 基础物理参数模块 ========== *)
acousticParameters = <|
  "referenceVelocity" -> 343, (* 参考声速 c0 *)
  "lossVelocity" -> Function[gainFactor, 343*(1 + gainFactor*I)], (* 损耗段声速 *)
  "gainVelocity" -> Function[gainFactor, 343*(1 - gainFactor*I)], (* 增益段声速 *)
  "waveNumber" -> Function[{frequency, velocity}, 2*Pi*frequency/velocity] (* 波数计算 *)
|>;

(* ========== 传输矩阵计算模块 ========== *)
transferMatrixCalculator = <|
  "lossSegment" -> Function[{frequency, length, gainFactor},
    Module[{velocity, waveNum},
      velocity = acousticParameters["lossVelocity"][gainFactor];
      waveNum = acousticParameters["waveNumber"][frequency, velocity];
      {{Cos[waveNum*length], I*Sin[waveNum*length]*velocity},
       {I*Sin[waveNum*length]/velocity, Cos[waveNum*length]}}
    ]
  ],
  "gainSegment" -> Function[{frequency, length, gainFactor},
    Module[{velocity, waveNum},
      velocity = acousticParameters["gainVelocity"][gainFactor];
      waveNum = acousticParameters["waveNumber"][frequency, velocity];
      {{Cos[waveNum*length], I*Sin[waveNum*length]*velocity},
       {I*Sin[waveNum*length]/velocity, Cos[waveNum*length]}}
    ]
  ],
  "combinedSystem" -> Function[{frequency, length, gainFactor},
    transferMatrixCalculator["lossSegment"][frequency, length, gainFactor] .
    transferMatrixCalculator["gainSegment"][frequency, length, gainFactor]
  ]
|>;

(* ========== 散射系数计算模块 ========== *)
scatteringCoefficients = <|
  "transmissionCoeff" -> Function[{frequency, length, gainFactor},
    Module[{transferMatrix, c0, denominator},
      transferMatrix = transferMatrixCalculator["combinedSystem"][frequency, length, gainFactor];
      c0 = acousticParameters["referenceVelocity"];
      denominator = transferMatrix[[1,1]] + transferMatrix[[1,2]]/c0 +
                   transferMatrix[[2,1]]*c0 + transferMatrix[[2,2]];
      2/denominator
    ]
  ],
  "leftReflectionCoeff" -> Function[{frequency, length, gainFactor},
    Module[{transferMatrix, c0, numerator, denominator},
      transferMatrix = transferMatrixCalculator["combinedSystem"][frequency, length, gainFactor];
      c0 = acousticParameters["referenceVelocity"];
      numerator = transferMatrix[[1,1]] + transferMatrix[[1,2]]/c0 -
                 transferMatrix[[2,1]]*c0 - transferMatrix[[2,2]];
      denominator = transferMatrix[[1,1]] + transferMatrix[[1,2]]/c0 +
                   transferMatrix[[2,1]]*c0 + transferMatrix[[2,2]];
      numerator/denominator
    ]
  ],
  "rightReflectionCoeff" -> Function[{frequency, length, gainFactor},
    Module[{transferMatrix, c0, numerator, denominator},
      transferMatrix = transferMatrixCalculator["combinedSystem"][frequency, length, gainFactor];
      c0 = acousticParameters["referenceVelocity"];
      numerator = -transferMatrix[[1,1]] + transferMatrix[[1,2]]/c0 -
                  transferMatrix[[2,1]]*c0 + transferMatrix[[2,2]];
      denominator = transferMatrix[[1,1]] + transferMatrix[[1,2]]/c0 +
                   transferMatrix[[2,1]]*c0 + transferMatrix[[2,2]];
      numerator/denominator
    ]
  ]
|>;

(* ========== 散射参数（功率）计算模块 ========== *)
scatteringParameters = <|
  "transmissionPower" -> Function[{frequency, length, gainFactor},
    Power[Abs[scatteringCoefficients["transmissionCoeff"][frequency, length, gainFactor]], 2]
  ],
  "leftReflectionPower" -> Function[{frequency, length, gainFactor},
    Power[Abs[scatteringCoefficients["leftReflectionCoeff"][frequency, length, gainFactor]], 2]
  ],
  "rightReflectionPower" -> Function[{frequency, length, gainFactor},
    Power[Abs[scatteringCoefficients["rightReflectionCoeff"][frequency, length, gainFactor]], 2]
  ]
|>;

(* ========== 系统配置参数 ========== *)
systemConfig = <|
  "defaultLength" -> 0.12,        (* 默认长度 *)
  "defaultGainFactor" -> 0.2,     (* 默认增益因子 *)
  "frequencyRange" -> {1, 4200},  (* 频率范围 *)
  "gainFactorRange" -> {0, 1},    (* 增益因子范围 *)
  "complexFreqImagRange" -> {0, 500} (* 复频率虚部范围 *)
|>;

(* ========== 向后兼容的别名（保持原有接口） ========== *)
(* 基础参数别名 *)
c0 = acousticParameters["referenceVelocity"];
myL = systemConfig["defaultLength"];
mya = systemConfig["defaultGainFactor"];

(* 函数别名 *)
bigT1tsm = scatteringParameters["transmissionPower"];
bigT1leftR = scatteringParameters["leftReflectionPower"];
bigT1rightR = scatteringParameters["rightReflectionPower"];