(*单元格1：基本定义*)
c0 = 343;
c1 = Function[a, 343*(1 + a*I)];(*loss*)
c2 = Function[a, 343*(1 - a*I)];(*gain*)
k = Function[{f, c}, 2*Pi*f/c];
lossT = Function[{f, L, 
    a}, {{Cos[k[f, c1[a]]*L], 
     I*Sin[k[f, c1[a]]*L]*c1[a]}, {I*Sin[k[f, c1[a]]*L]/c1[a], 
     Cos[k[f, c1[a]]*L]}}];
gainT = Function[{f, L, 
    a}, {{Cos[k[f, c2[a]]*L], 
     I*Sin[k[f, c2[a]]*L]*c2[a]}, {I*Sin[k[f, c2[a]]*L]/c2[a], 
     Cos[k[f, c2[a]]*L]}}];
(*下面系统为单loss+单gain*)
bigT1 = Function[{f, L, a}, lossT[f, L, a] . gainT[f, L, a]];
(*下面为透射*)
bigT1tsm = 
  Function[{f, L, a}, 
   Power[2/Abs[
      bigT1[f, L, a][[1, 1]] + bigT1[f, L, a][[1, 2]]/c0 + 
       bigT1[f, L, a][[2, 1]]*c0 + bigT1[f, L, a][[2, 2]]], 2]];
(*下面为左侧的反射*)
bigT1leftR = 
  Function[{f, L, a}, 
   Power[Abs[(bigT1[f, L, a][[1, 1]] + bigT1[f, L, a][[1, 2]]/c0 - 
        bigT1[f, L, a][[2, 1]]*c0 - 
        bigT1[f, L, a][[2, 2]])/(bigT1[f, L, a][[1, 1]] + 
        bigT1[f, L, a][[1, 2]]/c0 + bigT1[f, L, a][[2, 1]]*c0 + 
        bigT1[f, L, a][[2, 2]])], 2]];
(*下面为右侧的反射*)
bigT1rightR = 
  Function[{f, L, a}, 
   Power[Abs[(-bigT1[f, L, a][[1, 1]] + bigT1[f, L, a][[1, 2]]/c0 - 
        bigT1[f, L, a][[2, 1]]*c0 + 
        bigT1[f, L, a][[2, 2]])/(bigT1[f, L, a][[1, 1]] + 
        bigT1[f, L, a][[1, 2]]/c0 + bigT1[f, L, a][[2, 1]]*c0 + 
        bigT1[f, L, a][[2, 2]])], 2]];
(*暂定参数*)
myL = 0.12;
mya = 0.2;